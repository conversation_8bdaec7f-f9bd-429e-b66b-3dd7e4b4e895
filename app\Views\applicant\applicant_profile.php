<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('styles') ?>
<style>
/* Basic animations for smooth transitions */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Processing indicator animation for AI text extraction */
.fa-robot {
    animation: robotPulse 2s ease-in-out infinite;
}

@keyframes robotPulse {
    0%, 100% {
        opacity: 0.7;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

/* Smooth transition for public service file number field */
#public_service_file_number_div {
    transition: all 0.3s ease-in-out;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">Profile Management</h1>
                    <p class="text-muted">Update your profile information in different sections</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column - Profile Photo and Quick Links -->
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <?php if (!empty($applicant['id_photo_path'])): ?>
                            <img src="<?= base_url($applicant['id_photo_path']) ?>" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;">
                        <?php else: ?>
                            <div class="rounded-circle bg-light d-inline-flex align-items-center justify-content-center" style="width: 150px; height: 150px;">
                                <i class="fas fa-user fa-4x text-secondary"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <h5 class="mb-1"><?= esc($applicant['first_name']) ?> <?= esc($applicant['last_name']) ?></h5>
                    <p class="text-muted mb-3"><?= esc($applicant['current_position'] ?? 'Position not set') ?></p>

                    <!-- Quick Upload Photo Button -->
                    <form action="<?= base_url('applicant/profile/upload-photo') ?>" method="post" enctype="multipart/form-data" id="photoForm" class="mb-3">
                        <?= csrf_field() ?>
                        <div class="d-grid">
                            <label class="btn btn-outline-primary">
                                <i class="fas fa-camera me-2"></i>Change Photo
                                <input type="file" name="id_photo" class="d-none" accept="image/*">
                            </label>
                        </div>
                    </form>

                    <!-- Navigation Links -->
                    <div class="list-group">
                        <!-- 1. Personal Information -->
                        <a href="#personalInfo" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-user me-3"></i>
                                <div>
                                    <h6 class="mb-0">Personal Information</h6>
                                    <small class="text-muted">Basic details and contact info</small>
                                </div>
                            </div>
                        </a>
                        <!-- 2. Documents & ID -->
                        <a href="#documents" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-id-card me-3"></i>
                                <div>
                                    <h6 class="mb-0">Documents & ID</h6>
                                    <small class="text-muted">Identification and records</small>
                                </div>
                            </div>
                        </a>
                        <!-- 3. Employment -->
                        <a href="#employment" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-briefcase me-3"></i>
                                <div>
                                    <h6 class="mb-0">Employment</h6>
                                    <small class="text-muted">Current work information</small>
                                </div>
                            </div>
                        </a>
                        <!-- 4. Work Experience -->
                        <a href="#experiences" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-history me-3"></i>
                                <div>
                                    <h6 class="mb-0">Work Experience</h6>
                                    <small class="text-muted">Previous employment history</small>
                                </div>
                            </div>
                        </a>
                        <!-- 5. Education -->
                        <a href="#education" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-graduation-cap me-3"></i>
                                <div>
                                    <h6 class="mb-0">Education</h6>
                                    <small class="text-muted">Academic qualifications</small>
                                </div>
                            </div>
                        </a>
                        <!-- 6. Files -->
                        <a href="#files" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-alt me-3"></i>
                                <div>
                                    <h6 class="mb-0">Documents & Files</h6>
                                    <small class="text-muted">Upload and manage files</small>
                                </div>
                            </div>
                        </a>
                        <!-- 7. Family Information -->
                        <a href="#family" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-users me-3"></i>
                                <div>
                                    <h6 class="mb-0">Family Information</h6>
                                    <small class="text-muted">Family details and dependents</small>
                                </div>
                            </div>
                        </a>
                        <!-- 8. Additional Information -->
                        <a href="#additional" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-star me-3"></i>
                                <div>
                                    <h6 class="mb-0">Achievements</h6>
                                    <small class="text-muted">Awards and accomplishments</small>
                                </div>
                            </div>
                        </a>
                        <!-- 9. Security Settings -->
                        <a href="#security" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-lock me-3"></i>
                                <div>
                                    <h6 class="mb-0">Security Settings</h6>
                                    <small class="text-muted">Password and account security</small>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Forms -->
        <div class="col-md-9">
            <!-- Personal Information Section -->
            <div class="card mb-4" id="personalInfo">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Personal Information</h5>
                    <!-- <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleEdit('personalForm')">
                        <i class="fas fa-edit me-2"></i>Edit
                    </button> -->
                </div>
                <div class="card-body">
                    <form action="<?= base_url('applicant/profile/update-personal') ?>" method="post" id="personalForm" class="needs-validation" novalidate>
                        <?= csrf_field() ?>
                        <input type="hidden" name="scroll_position" id="personal_scroll_position">

                        <!-- Display validation errors if any -->
                        <?php if (session()->getFlashdata('errors')): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                        <li><?= esc($error) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="first_name" value="<?= esc($applicant['first_name'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="last_name" value="<?= esc($applicant['last_name'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Gender <span class="text-danger">*</span></label>
                                <select class="form-select" name="gender" required>
                                    <option value="">Select Gender</option>
                                    <option value="male" <?= ($applicant['gender'] ?? '') == 'male' ? 'selected' : '' ?>>Male</option>
                                    <option value="female" <?= ($applicant['gender'] ?? '') == 'female' ? 'selected' : '' ?>>Female</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Date of Birth <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" name="dobirth" value="<?= esc($applicant['dobirth'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Contact Number <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" name="contact_details" value="<?= esc($applicant['contact_details'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Email Address</label>
                                <div class="input-group">
                                    <input type="email" class="form-control" value="<?= esc($applicant['email'] ?? '') ?>" readonly>
                                    <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#updateEmailModal">
                                        <i class="fas fa-edit"></i> Change
                                    </button>
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Current Address</label>
                                <textarea class="form-control" name="location_address" rows="3"><?= esc($applicant['location_address'] ?? '') ?></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Place of Origin</label>
                                <input type="text" class="form-control" name="place_of_origin" value="<?= esc($applicant['place_of_origin'] ?? '') ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Citizenship</label>
                                <input type="text" class="form-control" name="citizenship" value="<?= esc($applicant['citizenship'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary" onclick="saveScrollPosition('personal')">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Documents & ID Section -->
            <div class="card mb-4" id="documents">
                <div class="card-header">
                    <h5 class="card-title mb-0">Identifications</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('applicant/profile/update-documents') ?>" method="post" id="documentsForm">
                        <?= csrf_field() ?>
                        <div class="row g-3">
                            <div class="col-12">
                                <label class="form-label">ID Numbers (NID, Passport, Driving License)</label>
                                <textarea class="form-control" name="id_numbers" rows="2" placeholder="Enter your identification numbers..."><?= esc($applicant['id_numbers'] ?? '') ?></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Criminal Record (if any)</label>
                                <textarea class="form-control" name="offence_convicted" rows="2" placeholder="Leave blank if not convicted"><?= esc($applicant['offence_convicted'] ?? '') ?></textarea>
                            </div>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">Update Identification</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Employment Information -->
            <div class="card mb-4" id="employment">
                <div class="card-header">
                    <h5 class="card-title mb-0">Employment Information</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('applicant/profile/update-employment') ?>" method="post" id="employmentForm">
                        <?= csrf_field() ?>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Current Employer</label>
                                <input type="text" class="form-control" name="current_employer" value="<?= esc($applicant['current_employer'] ?? '') ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Current Position</label>
                                <input type="text" class="form-control" name="current_position" value="<?= esc($applicant['current_position'] ?? '') ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Current Salary</label>
                                <input type="text" class="form-control" name="current_salary" value="<?= esc($applicant['current_salary'] ?? '') ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Are you a Public Servant?</label>
                                <select class="form-select" name="is_public_servant" id="is_public_servant" onchange="togglePublicServiceFileNumber()">
                                    <option value="0" <?= ($applicant['is_public_servant'] ?? 0) == 0 ? 'selected' : '' ?>>No</option>
                                    <option value="1" <?= ($applicant['is_public_servant'] ?? 0) == 1 ? 'selected' : '' ?>>Yes</option>
                                </select>
                            </div>
                            <div class="col-md-6" id="public_service_file_number_div" style="display: <?= ($applicant['is_public_servant'] ?? 0) == 1 ? 'block' : 'none' ?>;">
                                <label class="form-label">Public Service File Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="public_service_file_number" id="public_service_file_number" value="<?= esc($applicant['public_service_file_number'] ?? '') ?>" placeholder="Enter your file number">
                            </div>
                            <div class="col-md-6" id="employee_of_org_div" style="display: <?= ($applicant['is_public_servant'] ?? 0) == 1 ? 'block' : 'none' ?>;">
                                <label class="form-label">Employee of Organization</label>
                                <select class="form-select" name="employee_of_org_id" id="employee_of_org_id">
                                    <?php if (isset($organizations) && is_array($organizations)): ?>
                                        <?php foreach ($organizations as $org_id => $org_name): ?>
                                            <option value="<?= esc($org_id) ?>" <?= ($applicant['employee_of_org_id'] ?? '') == $org_id ? 'selected' : '' ?>>
                                                <?= esc($org_name) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <option value="">Not Listed</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">How did you hear about us?</label>
                                <select class="form-select" name="how_did_you_hear_about_us">
                                    <option value="">Select Source</option>
                                    <option value="newspaper" <?= ($applicant['how_did_you_hear_about_us'] ?? '') == 'newspaper' ? 'selected' : '' ?>>Newspaper</option>
                                    <option value="social_media" <?= ($applicant['how_did_you_hear_about_us'] ?? '') == 'social_media' ? 'selected' : '' ?>>Social Media</option>
                                    <option value="website" <?= ($applicant['how_did_you_hear_about_us'] ?? '') == 'website' ? 'selected' : '' ?>>Website</option>
                                    <option value="referral" <?= ($applicant['how_did_you_hear_about_us'] ?? '') == 'referral' ? 'selected' : '' ?>>Referral</option>
                                    <option value="other" <?= ($applicant['how_did_you_hear_about_us'] ?? '') == 'other' ? 'selected' : '' ?>>Other</option>
                                </select>
                            </div>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">Update Employment</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Work Experience -->
            <div class="card mb-4" id="experiences">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Work Experience</h5>
                    <button type="button" class="btn btn-sm btn-primary" onclick="addExperience()">
                        <i class="fas fa-plus me-2"></i>Add Experience
                    </button>
                </div>
                <div class="card-body">
                    <div id="experiencesList">
                        <?php if (empty($experiences)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-briefcase fa-3x mb-3"></i>
                                <p>No work experience added yet.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($experiences as $exp): ?>
                                <div class="experience-item mb-4 border-bottom pb-3" data-id="<?= $exp['id'] ?>">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h5 class="mb-1"><?= esc($exp['position']) ?></h5>
                                            <h6 class="text-primary mb-1"><?= esc($exp['employer']) ?></h6>
                                            <p class="text-muted small mb-2">
                                                <?= date('M Y', strtotime($exp['date_from'])) ?> -
                                                <?php
                                                    if (empty($exp['date_to']) || $exp['date_to'] == '0000-00-00' || $exp['date_to'] == null) {
                                                        echo 'Present';
                                                    } else {
                                                        echo date('M Y', strtotime($exp['date_to']));
                                                    }
                                                ?>
                                            </p>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editModal<?= $exp['id'] ?>">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $exp['id'] ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <?php if (!empty($exp['employer_contacts_address'])): ?>
                                        <p class="text-muted small mb-2">
                                            <i class="fas fa-map-marker-alt me-2"></i><?= esc($exp['employer_contacts_address']) ?>
                                        </p>
                                    <?php endif; ?>
                                    <?php if (!empty($exp['work_description'])): ?>
                                        <p class="mb-2"><?= nl2br(esc($exp['work_description'])) ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($exp['achievements'])): ?>
                                        <div class="achievements">
                                            <strong class="text-success">Key Achievements:</strong>
                                            <p class="mb-0"><?= nl2br(esc($exp['achievements'])) ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Edit Modal for each experience -->
                                <div class="modal fade" id="editModal<?= $exp['id'] ?>" tabindex="-1" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Edit Work Experience</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <form id="editForm<?= $exp['id'] ?>" action="<?= base_url('applicant/profile/update-experience') ?>" method="post">
                                                    <?= csrf_field() ?>
                                                    <input type="hidden" name="id" value="<?= $exp['id'] ?>">
                                                    <input type="hidden" name="scroll_position" id="edit_scroll_position_<?= $exp['id'] ?>">

                                                    <!-- Display validation errors if any -->
                                                    <?php if (session()->getFlashdata('errors')): ?>
                                                        <div class="alert alert-danger">
                                                            <ul class="mb-0">
                                                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                                                    <li><?= esc($error) ?></li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        </div>
                                                    <?php endif; ?>

                                                    <div class="mb-3">
                                                        <label class="form-label">Position <span class="text-danger">*</span></label>
                                                        <input type="text" class="form-control" name="position" value="<?= esc($exp['position']) ?>" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Employer <span class="text-danger">*</span></label>
                                                        <input type="text" class="form-control" name="employer" value="<?= esc($exp['employer']) ?>" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Employer Address & Contacts</label>
                                                        <textarea class="form-control" name="employer_contacts_address" rows="2"><?= esc($exp['employer_contacts_address']) ?></textarea>
                                                    </div>
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <label class="form-label">Date From <span class="text-danger">*</span></label>
                                                            <input type="date" class="form-control" name="date_from" value="<?= !empty($exp['date_from']) && $exp['date_from'] != '0000-00-00' ? esc($exp['date_from']) : '' ?>" required>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label class="form-label">Date To</label>
                                                            <input type="date" class="form-control" name="date_to" value="<?= !empty($exp['date_to']) && $exp['date_to'] != '0000-00-00' ? esc($exp['date_to']) : '' ?>">
                                                            <small class="text-muted">Leave blank if current position</small>
                                                        </div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Work Description</label>
                                                        <textarea class="form-control" name="work_description" rows="3"><?= esc($exp['work_description']) ?></textarea>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Achievements</label>
                                                        <textarea class="form-control" name="achievements" rows="3"><?= esc($exp['achievements']) ?></textarea>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <button type="button" class="btn btn-primary" onclick="updateExperience(<?= $exp['id'] ?>)">Update</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Delete Modal for each experience -->
                                <div class="modal fade" id="deleteModal<?= $exp['id'] ?>" tabindex="-1" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Delete Work Experience</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete this work experience at <?= esc($exp['employer']) ?>?</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <button type="button" class="btn btn-danger" onclick="confirmDelete(<?= $exp['id'] ?>)">Delete</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Education -->
            <div class="card mb-4" id="education">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Education</h5>
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addEducationModal">
                        <i class="fas fa-plus me-2"></i>Add Education
                    </button>
                </div>
                <div class="card-body">
                    <?php if (empty($education)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                            <p>No education records found.</p>
                        </div>
                    <?php else: ?>
                        <div class="timeline">
                            <?php foreach ($education as $edu): ?>
                                <div class="timeline-item mb-4 border-bottom pb-3">
                                    <div class="timeline-content">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h5 class="mb-1"><?= esc($edu['institution']) ?></h5>
                                                <p class="mb-1"><?= esc($edu['course']) ?></p>
                                                <p class="text-muted mb-1">
                                                    <?= date('M Y', strtotime($edu['date_from'])) ?> -
                                                    <?php if (empty($edu['date_to']) || $edu['date_to'] == '0000-00-00'): ?>
                                                        Present
                                                    <?php else: ?>
                                                        <?= date('M Y', strtotime($edu['date_to'])) ?>
                                                    <?php endif; ?>
                                                </p>
                                                <p class="mb-1">
                                                    <?php
                                                    $education_level_name = '';
                                                    if (!empty($education_data)) {
                                                        foreach ($education_data as $edu_level) {
                                                            if ($edu_level['id'] == $edu['education_level']) {
                                                                $education_level_name = $edu_level['name'];
                                                                break;
                                                            }
                                                        }
                                                    }
                                                    if (empty($education_level_name)) {
                                                        $education_level_name = $education_levels[$edu['education_level']] ?? 'Unknown';
                                                    }
                                                    ?>
                                                    <span class="badge bg-info"><?= esc($education_level_name) ?></span>
                                                    <?php if (!empty($edu['units'])): ?>
                                                        <span class="text-muted">(<?= nl2br(esc($edu['units'])) ?> units)</span>
                                                    <?php endif; ?>
                                                </p>
                                            </div>
                                            <div>
                                                <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#editEducationModal<?= $edu['id'] ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#deleteEducationModal<?= $edu['id'] ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Edit Education Modal -->
                                <div class="modal fade" id="editEducationModal<?= $edu['id'] ?>" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Edit Education</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <form id="editEducationForm<?= $edu['id'] ?>">
                                                    <?= csrf_field() ?>
                                                    <input type="hidden" name="id" value="<?= $edu['id'] ?>">
                                                    <div class="mb-3">
                                                        <label class="form-label">Institution</label>
                                                        <input type="text" class="form-control" name="institution" value="<?= esc($edu['institution']) ?>" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Course/Program</label>
                                                        <input type="text" class="form-control" name="course" value="<?= esc($edu['course']) ?>" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Education Level</label>
                                                        <select class="form-select" name="education_level" required>
                                                            <option value="">Select Level</option>
                                                            <?php if (!empty($education_data)): ?>
                                                                <?php foreach ($education_data as $edu_level): ?>
                                                                    <option value="<?= $edu_level['id'] ?>" <?= isset($edu['education_level']) && $edu['education_level'] == $edu_level['id'] ? 'selected' : '' ?>>
                                                                        <?= esc($edu_level['name']) ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            <?php else: ?>
                                                                <?php foreach ($education_levels as $key => $level): ?>
                                                                    <option value="<?= $key ?>" <?= isset($edu['education_level']) && $edu['education_level'] == $key ? 'selected' : '' ?>>
                                                                        <?= esc($level) ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            <?php endif; ?>
                                                        </select>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Units (Optional)</label>
                                                        <textarea class="form-control" name="units" rows="3" placeholder="List the units/subjects under this course..."><?= esc($edu['units']) ?></textarea>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">Date From</label>
                                                            <input type="date" class="form-control" name="date_from"
                                                                   value="<?= $edu['date_from'] != '0000-00-00' ? $edu['date_from'] : '' ?>" required>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">Date To</label>
                                                            <input type="date" class="form-control" name="date_to"
                                                                   value="<?= $edu['date_to'] != '0000-00-00' ? $edu['date_to'] : '' ?>">
                                                            <small class="text-muted">Leave blank if current</small>
                                                        </div>
                                                    </div>
                                                    <div class="text-end">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <button type="button" class="btn btn-primary" onclick="updateEducation(<?= $edu['id'] ?>)">Save Changes</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Delete Education Modal -->
                                <div class="modal fade" id="deleteEducationModal<?= $edu['id'] ?>" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Delete Education Record</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete your education record at <?= esc($edu['institution']) ?>?</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <button type="button" class="btn btn-danger" onclick="confirmDeleteEducation(<?= $edu['id'] ?>)">Delete</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Files Section -->
            <div class="card mb-4" id="files">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Documents & Files</h5>
                    <a href="<?= base_url('applicant/profile/files/create') ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-2"></i>Upload File
                    </a>
                </div>
                <div class="card-body">
                    <!-- File Upload Warning -->
                    <div class="alert alert-info mb-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-info-circle me-2 mt-1"></i>
                            <div>
                                <strong>Important Notice:</strong> All uploaded documents will be analyzed and processed using AI for content extraction and analysis.
                                If your document has more than 20 pages, processing may take more than 5 minutes.
                                For faster processing, it is advisable to split large documents into several files with less than 20 pages per document.
                            </div>
                        </div>
                    </div>
                    <?php if (empty($files)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-file fa-3x mb-3"></i>
                            <p>No files uploaded yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>File</th>
                                        <th>Description</th>
                                        <th>Type</th>
                                        <th>Upload Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($files as $file): ?>
                                        <?php
                                        $file_extension = strtolower(pathinfo($file['file_path'], PATHINFO_EXTENSION));
                                        $file_icon = '';
                                        $file_color = '';

                                        switch($file_extension) {
                                            case 'pdf':
                                                $file_icon = 'fas fa-file-pdf';
                                                $file_color = 'text-danger';
                                                break;
                                            case 'doc':
                                            case 'docx':
                                                $file_icon = 'fas fa-file-word';
                                                $file_color = 'text-primary';
                                                break;
                                            case 'jpg':
                                            case 'jpeg':
                                            case 'png':
                                                $file_icon = 'fas fa-file-image';
                                                $file_color = 'text-success';
                                                break;
                                            default:
                                                $file_icon = 'fas fa-file';
                                                $file_color = 'text-secondary';
                                        }
                                        ?>
                                        <tr data-file-id="<?= $file['id'] ?>">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="<?= $file_icon ?> <?= $file_color ?> me-2"></i>
                                                    <div>
                                                        <div class="fw-medium"><?= esc($file['file_title']) ?></div>
                                                        
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="text-muted"><?= esc($file['file_description'] ?: 'No description') ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-light text-dark"><?= strtoupper($file_extension) ?></span>
                                            </td>
                                            <td><?= date('M d, Y', strtotime($file['created_at'])) ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url($file['file_path']) ?>"
                                                       class="btn btn-sm btn-outline-primary"
                                                       target="_blank"
                                                       title="View/Download">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('applicant/profile/files/' . $file['id'] . '/edit') ?>"
                                                       class="btn btn-sm btn-outline-secondary"
                                                       title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-danger"
                                                            onclick="confirmDeleteFile(<?= $file['id'] ?>)"
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Note: File upload and edit modals removed - now using separate pages for RESTful approach -->

            <!-- Extracted Text Modal -->
            <div class="modal fade" id="extractedTextModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Extracted Text</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold">File:</label>
                                <span id="extracted_text_filename"></span>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label class="form-label fw-bold mb-0">Extracted Content:</label>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <input type="radio" class="btn-check" name="viewMode" id="markdownView" checked>
                                        <label class="btn btn-outline-secondary" for="markdownView">Formatted</label>

                                        <input type="radio" class="btn-check" name="viewMode" id="rawView">
                                        <label class="btn btn-outline-secondary" for="rawView">Raw</label>
                                    </div>
                                </div>
                                <div class="border rounded p-3" style="max-height: 400px; overflow-y: auto; background-color: #f8f9fa;">
                                    <div id="extracted_text_formatted" style="display: block; line-height: 1.6;"></div>
                                    <pre id="extracted_text_raw" style="white-space: pre-wrap; margin: 0; font-family: inherit; display: none; line-height: 1.5;"></pre>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" onclick="copyExtractedText()">
                                <i class="fas fa-copy me-2"></i>Copy Text
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Family Information -->
            <div class="card mb-4" id="family">
                <div class="card-header">
                    <h5 class="card-title mb-0">Family Information</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('applicant/profile/update-family') ?>" method="post" id="familyForm">
                        <?= csrf_field() ?>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Marital Status</label>
                                <select class="form-select" name="marital_status" id="maritalStatus">
                                    <option value="">Select Status</option>
                                    <option value="single" <?= ($applicant['marital_status'] ?? '') == 'single' ? 'selected' : '' ?>>Single</option>
                                    <option value="married" <?= ($applicant['marital_status'] ?? '') == 'married' ? 'selected' : '' ?>>Married</option>
                                    <option value="divorced" <?= ($applicant['marital_status'] ?? '') == 'divorced' ? 'selected' : '' ?>>Divorced</option>
                                    <option value="widowed" <?= ($applicant['marital_status'] ?? '') == 'widowed' ? 'selected' : '' ?>>Widowed</option>
                                    <option value="separated" <?= ($applicant['marital_status'] ?? '') == 'separated' ? 'selected' : '' ?>>Separated</option>
                                </select>
                            </div>
                            <div class="col-md-6 marriage-details" style="display: none;">
                                <label class="form-label">Date of Marriage</label>
                                <input type="date" class="form-control" name="date_of_marriage" value="<?= esc($applicant['date_of_marriage'] ?? '') ?>">
                            </div>
                            <div class="col-12 marriage-details" style="display: none;">
                                <label class="form-label">Spouse's Employer</label>
                                <input type="text" class="form-control" name="spouse_employer" value="<?= esc($applicant['spouse_employer'] ?? '') ?>" placeholder="Employer name or public servant file number if government employee">
                            </div>
                            <div class="col-12">
                                <label class="form-label">Children Information</label>
                                <div id="childrenContainer">
                                    <?php
                                    $children = json_decode($applicant['children'] ?? '[]', true);
                                    foreach ($children as $index => $child):
                                    ?>
                                    <div class="row g-2 mb-2 child-entry">
                                        <div class="col-md-4">
                                            <input type="text" class="form-control" name="children[<?= $index ?>][name]" value="<?= esc($child['name']) ?>" placeholder="Child's Name">
                                        </div>
                                        <div class="col-md-4">
                                            <input type="date" class="form-control" name="children[<?= $index ?>][dob]" value="<?= esc($child['dob']) ?>">
                                        </div>
                                        <div class="col-md-3">
                                            <select class="form-select" name="children[<?= $index ?>][gender]">
                                                <option value="">Gender</option>
                                                <option value="Male" <?= $child['gender'] == 'Male' ? 'selected' : '' ?>>Male</option>
                                                <option value="Female" <?= $child['gender'] == 'Female' ? 'selected' : '' ?>>Female</option>
                                            </select>
                                        </div>
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-danger btn-sm remove-child"><i class="fas fa-times"></i></button>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <button type="button" class="btn btn-outline-secondary btn-sm mt-2" id="addChild">
                                    <i class="fas fa-plus me-2"></i>Add Child
                                </button>
                            </div>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">Update Family Information</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="card mb-4" id="additional">
                <div class="card-header">
                    <h5 class="card-title mb-0">Achievements & References</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('applicant/profile/update-additional') ?>" method="post" id="additionalForm">
                        <?= csrf_field() ?>
                        <input type="hidden" name="scroll_position" id="additional_scroll_position">
                        <div class="row g-3">
                            <div class="col-12">
                                <label class="form-label">Publications</label>
                                <textarea class="form-control" name="publications" rows="3" placeholder="List your publications with dates..."><?= esc($applicant['publications'] ?? '') ?></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Awards & Achievements</label>
                                <textarea class="form-control" name="awards" rows="3" placeholder="List your awards and achievements..."><?= esc($applicant['awards'] ?? '') ?></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Referees</label>
                                <textarea class="form-control" name="referees" rows="4" placeholder="Enter referee details (name, address, contact details)..."><?= esc($applicant['referees'] ?? '') ?></textarea>
                            </div>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">Update Additional Information</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="card mb-4" id="security">
                <div class="card-header">
                    <h5 class="card-title mb-0">Security Settings</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('applicant/profile/change-password') ?>" method="post" id="passwordForm">
                        <?= csrf_field() ?>
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label class="form-label">Current Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" name="current_password" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">New Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" name="new_password" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Confirm New Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" name="confirm_password" required>
                            </div>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">Change Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Experience Modal -->
<div class="modal fade" id="experienceModal" tabindex="-1" aria-labelledby="experienceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="experienceModalLabel">Add Work Experience</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="experienceForm" action="<?= base_url('applicant/profile/add-experience') ?>" method="post">
                    <?= csrf_field() ?>
                    <input type="hidden" name="scroll_position" id="experience_scroll_position">

                    <!-- Display validation errors if any -->
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <div class="mb-3">
                        <label class="form-label">Position <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="position" id="position" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Employer <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="employer" id="employer" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Employer Address & Contacts</label>
                        <textarea class="form-control" name="employer_contacts_address" id="employer_contacts_address" rows="2"></textarea>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Date From <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" name="date_from" id="date_from" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Date To</label>
                            <input type="date" class="form-control" name="date_to" id="date_to">
                            <small class="text-muted">Leave blank if current position</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Work Description</label>
                        <textarea class="form-control" name="work_description" id="work_description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Achievements</label>
                        <textarea class="form-control" name="achievements" id="achievements" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitExperienceForm()">Save Experience</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Education Modal -->
<div class="modal fade" id="addEducationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Education</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <?php
                // Initialize $edu with default values
                $edu = [
                    'education_level' => '',
                    'date_to' => '',
                    'units' => ''
                ];
                ?>
                <form id="addEducationForm">
                    <?= csrf_field() ?>
                    <div class="mb-3">
                        <label class="form-label">Institution Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="institution" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Course/Program <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="course" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Education Level <span class="text-danger">*</span></label>
                        <select class="form-select" name="education_level" required>
                            <option value="">Select Level</option>
                            <?php if (!empty($education_data)): ?>
                                <?php foreach ($education_data as $edu_level): ?>
                                    <option value="<?= $edu_level['id'] ?>" <?= isset($edu['education_level']) && $edu['education_level'] == $edu_level['id'] ? 'selected' : '' ?>>
                                        <?= esc($edu_level['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <?php foreach ($education_levels as $key => $level): ?>
                                    <option value="<?= $key ?>" <?= isset($edu['education_level']) && $edu['education_level'] == $key ? 'selected' : '' ?>>
                                        <?= esc($level) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Units (Optional)</label>
                        <textarea class="form-control" name="units" rows="3" placeholder="List the units/subjects under this course..."></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Date From <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" name="date_from" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Date To</label>
                            <input type="date" class="form-control" name="date_to"
                                   value="<?= $edu['date_to'] != '0000-00-00' ? $edu['date_to'] : '' ?>">
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="addEducation()">Add Education</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
// Add experiences data to JavaScript context
const experiences = <?= json_encode($experiences ?? []) ?>;

// Add files data to JavaScript context
const filesData = <?= json_encode($files ?? []) ?>;

// Storage access error handling
function handleStorageError() {
    try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
    } catch (e) {
        console.warn('Local storage is not available. Some features might be limited.');
    }
}

// Save current scroll position before form submission
function saveScrollPosition(section = '') {
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

    // Set the scroll position in the appropriate hidden field
    if (section === 'personal') {
        document.getElementById('personal_scroll_position').value = scrollPosition;
    }

    // Also save to localStorage as backup
    localStorage.setItem('profileScrollPosition', scrollPosition);
}

// Restore scroll position after page load
function restoreScrollPosition() {
    // Check for scroll parameter in URL first
    const urlParams = new URLSearchParams(window.location.search);
    const urlScrollPosition = urlParams.get('scroll');

    if (urlScrollPosition) {
        setTimeout(() => {
            window.scrollTo(0, parseInt(urlScrollPosition));
        }, 100);
        return;
    }

    // Fallback to localStorage
    const scrollPosition = localStorage.getItem('profileScrollPosition');
    if (scrollPosition) {
        setTimeout(() => {
            window.scrollTo(0, parseInt(scrollPosition));
        }, 100);
        localStorage.removeItem('profileScrollPosition');
    }
}

// Handle hash-based navigation (for section anchors)
function handleHashNavigation() {
    if (window.location.hash) {
        // Extract just the hash part without query parameters
        const hashPart = window.location.hash.split('?')[0];

        if (hashPart && hashPart.length > 1) {
            try {
                const target = document.querySelector(hashPart);
                if (target) {
                    setTimeout(() => {
                        target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }, 100);
                }
            } catch (e) {
                console.warn('Invalid hash selector:', hashPart, e);
            }
        }
    }
}

// Format date for input fields
function formatDateForInput(dateString) {
    if (!dateString || dateString === '0000-00-00' || dateString === 'null') {
        return '';
    }
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return '';
        }
        return date.toISOString().split('T')[0];
    } catch (e) {
        return '';
    }
}

// Experience form submission functions
function submitExperienceForm() {
    const form = document.getElementById('experienceForm');

    // Save scroll position before submission
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
    document.getElementById('experience_scroll_position').value = scrollPosition;

    // Submit form normally
    form.submit();
}

function updateExperience(id) {
    const form = document.getElementById('editForm' + id);

    // Save scroll position before submission
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
    document.getElementById('edit_scroll_position_' + id).value = scrollPosition;

    // Submit form normally to ApplicantController
    form.submit();
}

function confirmDelete(id) {
    if (confirm('Are you sure you want to delete this work experience?')) {
        // Save scroll position before submission
        const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?= base_url('applicant/profile/delete-experience') ?>/${id}`;

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);

        // Add scroll position
        const scrollInput = document.createElement('input');
        scrollInput.type = 'hidden';
        scrollInput.name = 'scroll_position';
        scrollInput.value = scrollPosition;
        form.appendChild(scrollInput);

        document.body.appendChild(form);
        form.submit();
    }
}

// Add the confirmDeleteEducation function
function confirmDeleteEducation(id) {
    // Save scroll position before submission
    saveScrollPosition();

    // Create a form and submit it
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `<?= base_url('applicant/profile/delete-education') ?>/${id}`;

    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '<?= csrf_token() ?>';
    csrfInput.value = '<?= csrf_hash() ?>';
    form.appendChild(csrfInput);

    document.body.appendChild(form);
    form.submit();
}

// Add the updateEducation function
function updateEducation(id) {
    const form = document.getElementById('editEducationForm' + id);

    // Save scroll position before submission
    saveScrollPosition();

    // Submit form normally to ApplicantController
    form.action = '<?= base_url('applicant/profile/update-education') ?>';
    form.method = 'POST';
    form.submit();
}

// Add the addEducation function
function addEducation() {
    const form = document.getElementById('addEducationForm');

    // Save scroll position before submission
    saveScrollPosition();

    // Submit form normally to ApplicantController
    form.action = '<?= base_url('applicant/profile/add-education') ?>';
    form.method = 'POST';
    form.submit();
}

// File upload form validation and submission
function validateFileUpload(form) {
    const fileTitle = form.querySelector('input[name="file_title"]').value.trim();
    const fileInput = form.querySelector('input[name="file"]');

    if (!fileTitle) {
        alert('Please provide a file title');
        return false;
    }

    if (!fileInput.files.length) {
        alert('Please select a file to upload');
        return false;
    }

    // Validate file size (25MB = 25600KB)
    const file = fileInput.files[0];
    if (file.size > 25600 * 1024) {
        alert('File size must be less than 25MB');
        return false;
    }

    // Validate file type
    const allowedTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'];
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
        alert('Please select a valid file type (PDF, DOC, DOCX, JPG, JPEG, PNG)');
        return false;
    }

    return true;
}

// Note: Modal-related functions removed - now using separate pages for RESTful approach







// Simple markdown renderer for basic formatting
function renderMarkdown(text) {
    if (!text) return '';

    let html = text;

    // Headers (process from largest to smallest to avoid conflicts)
    html = html.replace(/^### (.*$)/gim, '<h5 class="mt-3 mb-2">$1</h5>');
    html = html.replace(/^## (.*$)/gim, '<h4 class="mt-3 mb-2">$1</h4>');
    html = html.replace(/^# (.*$)/gim, '<h3 class="mt-3 mb-2">$1</h3>');

    // Bold and italic
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Code
    html = html.replace(/`(.*?)`/g, '<code class="bg-light px-1 rounded">$1</code>');

    // Blockquotes
    html = html.replace(/^> (.*$)/gim, '<blockquote class="border-start border-3 border-secondary ps-3 text-muted mb-2">$1</blockquote>');

    // Simple table detection and conversion
    html = html.replace(/\|(.+)\|/g, function(match, content) {
        const cells = content.split('|').map(cell => cell.trim()).filter(cell => cell);
        const cellsHtml = cells.map(cell => `<td class="border px-2 py-1">${cell}</td>`).join('');
        return `<tr>${cellsHtml}</tr>`;
    });

    // Wrap table rows in table
    html = html.replace(/(<tr>.*?<\/tr>)(?:\s*<br>\s*<tr>.*?<\/tr>)*/g, function(match) {
        return `<table class="table table-sm table-bordered mb-3">${match.replace(/<br>/g, '')}</table>`;
    });

    // Lists
    html = html.replace(/^\* (.*$)/gim, '<li>$1</li>');
    html = html.replace(/^- (.*$)/gim, '<li>$1</li>');
    html = html.replace(/^(\d+)\. (.*$)/gim, '<li>$2</li>');

    // Line breaks
    html = html.replace(/\n/g, '<br>');

    // Wrap consecutive <li> elements in <ul>
    html = html.replace(/(<li>.*?<\/li>)(?:\s*<br>\s*<li>.*?<\/li>)*/g, function(match) {
        return '<ul class="mb-2">' + match.replace(/<br>/g, '') + '</ul>';
    });

    return html;
}

// Show extracted text in modal
function showExtractedText(filename, extractedText) {
    document.getElementById('extracted_text_filename').textContent = filename;
    document.getElementById('extracted_text_content').textContent = extractedText;
}

// Copy extracted text to clipboard
function copyExtractedText() {
    // Get text content based on current view mode
    const isFormattedView = document.getElementById('markdownView').checked;
    let textContent;

    if (isFormattedView) {
        // For formatted view, get the plain text from the raw content
        textContent = document.getElementById('extracted_text_raw').textContent;
    } else {
        // For raw view, get the text directly
        textContent = document.getElementById('extracted_text_raw').textContent;
    }

    if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        navigator.clipboard.writeText(textContent).then(() => {
            // Show success feedback
            const copyBtn = event.target.closest('button');
            const originalText = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check me-2"></i>Copied!';
            copyBtn.classList.remove('btn-primary');
            copyBtn.classList.add('btn-success');

            setTimeout(() => {
                copyBtn.innerHTML = originalText;
                copyBtn.classList.remove('btn-success');
                copyBtn.classList.add('btn-primary');
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy text: ', err);
            fallbackCopyText(textContent);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyText(textContent);
    }
}

// Fallback copy method for older browsers
function fallbackCopyText(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        alert('Text copied to clipboard!');
    } catch (err) {
        console.error('Fallback copy failed: ', err);
        alert('Failed to copy text. Please select and copy manually.');
    }

    document.body.removeChild(textArea);
}

// File Upload Functions (Standard Form Submission)
// Note: All file upload now uses standard form submission to ApplicantController









function confirmDeleteFile(id) {
    if (confirm('Are you sure you want to delete this file?')) {
        // Save scroll position before submission
        saveScrollPosition();

        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?= base_url('applicant/profile/delete-file') ?>/${id}`;

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();
    }
}

$(document).ready(function() {
    // Handle storage access
    handleStorageError();

    // Restore scroll position after page load
    restoreScrollPosition();

    // Handle hash-based navigation
    handleHashNavigation();

    // Note: File upload form submission handlers removed - now handled in separate pages

    // Handle personal form submission specifically
    $('#personalForm').on('submit', function(e) {
        console.log('Personal form submission started');

        // Save scroll position
        const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
        $('#personal_scroll_position').val(scrollPosition);

        // Log form data for debugging
        const formData = new FormData(this);
        console.log('Form data being submitted:');
        for (let [key, value] of formData.entries()) {
            console.log(key + ': ' + value);
        }

        // Allow normal form submission
        return true;
    });

    // Add scroll position saving to all other forms
    $('form:not(#personalForm)').on('submit', function(e) {
        const formId = $(this).attr('id');

        // Handle file edit validation
        if (formId === 'editFileForm') {
            if (!validateFileEdit(this)) {
                e.preventDefault();
                return false;
            }
            // Close modal before submission
            const modal = bootstrap.Modal.getInstance(document.getElementById('editFileModal'));
            if (modal) {
                modal.hide();
            }
        }

        saveScrollPosition();

        // For specific forms, also set the hidden input
        const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

        if (formId === 'additionalForm') {
            $('#additional_scroll_position').val(scrollPosition);
        } else if (formId === 'uploadFileForm') {
            $('#upload_scroll_position').val(scrollPosition);
        } else if (formId === 'editFileForm') {
            $('#edit_scroll_position').val(scrollPosition);
        }
    });

    // Handle marital status change
    $('#maritalStatus').on('change', function() {
        if ($(this).val() === 'married') {
            $('.marriage-details').slideDown();
        } else {
            $('.marriage-details').slideUp();
        }
    });

    // Trigger initial marital status check
    $('#maritalStatus').trigger('change');

    // Handle adding new child
    $('#addChild').on('click', function() {
        const index = $('.child-entry').length;
        const newChild = `
            <div class="row g-2 mb-2 child-entry">
                <div class="col-md-4">
                    <input type="text" class="form-control" name="children[${index}][name]" placeholder="Child's Name">
                </div>
                <div class="col-md-4">
                    <input type="date" class="form-control" name="children[${index}][dob]">
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="children[${index}][gender]">
                        <option value="">Gender</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger btn-sm remove-child"><i class="fas fa-times"></i></button>
                </div>
            </div>
        `;
        $('#childrenContainer').append(newChild);
    });

    // Handle removing child
    $(document).on('click', '.remove-child', function() {
        $(this).closest('.child-entry').remove();
    });

    // All forms now use standard CodeIgniter 4 form submission instead of AJAX

    // Smooth scroll to sections
    $('.list-group-item').on('click', function(e) {
        e.preventDefault();
        const target = $($(this).attr('href'));
        $('html, body').animate({
            scrollTop: target.offset().top - 20
        }, 500);
    });

    // Photo upload handling - using standard form submission
    $('input[name="id_photo"]').on('change', function() {
        const file = this.files[0];
        if (file) {
            // Save scroll position before submission
            saveScrollPosition();

            // Submit the form normally
            $('#photoForm')[0].submit();
        }
    });

    // Initialize the experience modal
    const experienceModal = new bootstrap.Modal(document.getElementById('experienceModal'));

    // Function to add experience - show modal
    window.addExperience = function() {
        document.getElementById('experienceForm').reset();
        document.getElementById('experienceModalLabel').textContent = 'Add Work Experience';
        experienceModal.show();
    };

    // Handle scroll position restoration when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Handle scroll position restoration from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const scrollParam = urlParams.get('scroll');

        if (scrollParam) {
            setTimeout(() => {
                window.scrollTo(0, parseInt(scrollParam));
            }, 100);
        } else {
            // Handle hash-based navigation for section anchors
            handleHashNavigation();
        }

        // Initialize public service file number field visibility
        if (typeof togglePublicServiceFileNumber === 'function') {
            togglePublicServiceFileNumber();
        }
    });

    // Edit Education Form Handler - using standard form submission
    $('[id^=editEducationForm]').each(function() {
        $(this).on('submit', function(e) {
            // Save scroll position before submission
            saveScrollPosition();

            // Allow normal form submission (remove preventDefault)
            // Form will submit normally to its action URL
        });
    });

    // Handle extracted text modal
    $(document).on('click', '.view-extracted-text', function() {
        const fileId = $(this).data('file-id');
        const file = filesData.find(f => f.id == fileId);

        if (file) {
            const extractedText = file.file_extracted_texts || 'No text extracted';

            document.getElementById('extracted_text_filename').textContent = file.file_title;

            // Set both formatted and raw content
            document.getElementById('extracted_text_formatted').innerHTML = renderMarkdown(extractedText);
            document.getElementById('extracted_text_raw').textContent = extractedText;

            // Reset to formatted view
            document.getElementById('markdownView').checked = true;
            document.getElementById('extracted_text_formatted').style.display = 'block';
            document.getElementById('extracted_text_raw').style.display = 'none';
        } else {
            document.getElementById('extracted_text_filename').textContent = 'Unknown File';
            document.getElementById('extracted_text_formatted').innerHTML = 'Error: File data not found';
            document.getElementById('extracted_text_raw').textContent = 'Error: File data not found';
        }
    });

    // Handle view mode toggle
    $(document).on('change', 'input[name="viewMode"]', function() {
        if (this.id === 'markdownView') {
            document.getElementById('extracted_text_formatted').style.display = 'block';
            document.getElementById('extracted_text_raw').style.display = 'none';
        } else {
            document.getElementById('extracted_text_formatted').style.display = 'none';
            document.getElementById('extracted_text_raw').style.display = 'block';
        }
    });

    // Reset upload form when modal is shown
    $('#addFileModal').on('show.bs.modal', function() {
        document.getElementById('uploadFileForm').reset();
    });

});

// Toggle Public Service File Number and Organization fields visibility
function togglePublicServiceFileNumber() {
    const isPublicServant = document.getElementById('is_public_servant').value;
    const fileNumberDiv = document.getElementById('public_service_file_number_div');
    const fileNumberInput = document.getElementById('public_service_file_number');
    const orgDiv = document.getElementById('employee_of_org_div');
    const orgSelect = document.getElementById('employee_of_org_id');

    if (isPublicServant == '1') {
        fileNumberDiv.style.display = 'block';
        fileNumberInput.required = true;
        orgDiv.style.display = 'block';
    } else {
        fileNumberDiv.style.display = 'none';
        fileNumberInput.required = false;
        fileNumberInput.value = ''; // Clear the field when hidden
        orgDiv.style.display = 'none';
        orgSelect.value = ''; // Clear the organization selection when hidden
    }
}

// Email Update Modal Functions
let emailCheckTimeout;
let isEmailValid = false;

function validateEmailInput() {
    const email = document.getElementById('new_email').value.trim();
    const emailIcon = document.getElementById('email-icon');
    const emailFeedback = document.getElementById('email-feedback');
    const submitBtn = document.getElementById('email-submit-btn');

    // Clear previous timeout
    if (emailCheckTimeout) {
        clearTimeout(emailCheckTimeout);
    }

    // Reset state
    isEmailValid = false;
    submitBtn.disabled = true;

    if (email === '') {
        emailIcon.className = '';
        emailFeedback.textContent = '';
        return;
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        emailIcon.className = 'fas fa-times-circle text-danger';
        emailFeedback.textContent = 'Please enter a valid email address';
        emailFeedback.className = 'text-danger';
        return;
    }

    // Show checking state
    emailIcon.className = 'fas fa-spinner fa-spin text-primary';
    emailFeedback.textContent = 'Checking email availability...';
    emailFeedback.className = 'text-primary';

    // Debounce the AJAX call
    emailCheckTimeout = setTimeout(function() {
        checkEmailAvailability(email);
    }, 800);
}

function checkEmailAvailability(email) {
    fetch('<?= base_url('applicant/profile/check-email-availability') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            'email': email,
            '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        const emailIcon = document.getElementById('email-icon');
        const emailFeedback = document.getElementById('email-feedback');
        const submitBtn = document.getElementById('email-submit-btn');

        if (data.success && data.available) {
            // Email is available
            emailIcon.className = 'fas fa-check-circle text-success';
            emailFeedback.textContent = 'Email address is available';
            emailFeedback.className = 'text-success';
            isEmailValid = true;
            submitBtn.disabled = false;
        } else {
            // Email is not available or error
            emailIcon.className = 'fas fa-times-circle text-danger';
            emailFeedback.textContent = data.message || 'Email address is not available';
            emailFeedback.className = 'text-danger';
            isEmailValid = false;
            submitBtn.disabled = true;
        }
    })
    .catch(error => {
        const emailIcon = document.getElementById('email-icon');
        const emailFeedback = document.getElementById('email-feedback');
        const submitBtn = document.getElementById('email-submit-btn');

        emailIcon.className = 'fas fa-exclamation-triangle text-warning';
        emailFeedback.textContent = 'Error checking email availability. Please try again.';
        emailFeedback.className = 'text-warning';
        isEmailValid = false;
        submitBtn.disabled = true;

        console.error('Email check error:', error);
    });
}

// Reset modal when it's closed
document.getElementById('updateEmailModal').addEventListener('hidden.bs.modal', function () {
    document.getElementById('updateEmailForm').reset();
    document.getElementById('email-icon').className = '';
    document.getElementById('email-feedback').textContent = '';
    document.getElementById('email-submit-btn').disabled = true;
    isEmailValid = false;
});
</script>

<!-- Update Email Modal -->
<div class="modal fade" id="updateEmailModal" tabindex="-1" aria-labelledby="updateEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateEmailModalLabel">Update Email Address</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= base_url('applicant/profile/update-email') ?>" method="post" id="updateEmailForm">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="current_email" class="form-label">Current Email Address</label>
                        <input type="email" class="form-control" id="current_email" value="<?= esc($applicant['email'] ?? '') ?>" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="new_email" class="form-label">New Email Address <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="email" class="form-control" id="new_email" name="new_email" required onkeyup="validateEmailInput()">
                            <span class="input-group-text">
                                <i id="email-icon"></i>
                            </span>
                        </div>
                        <div id="email-feedback" class="form-text"></div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Please make sure you have access to the new email address as it will be used for future communications.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="email-submit-btn" disabled>Update Email</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->endSection() ?>